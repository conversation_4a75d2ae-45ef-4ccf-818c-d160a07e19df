import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile
} from '../helpers/shared-context';
import { createGhostAPIClient } from '../helpers/ghost-cleanup';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { test } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Commands: Sync All", () => {
  const context = setupE2ETestHooks();
  let ghostAPI: any = null;
  let createdPostIds: string[] = [];

  // Setup Ghost API client
  beforeAll(async () => {
    ghostAPI = createGhostAPIClient();
    if (!ghostAPI) {
      throw new Error('Ghost API not available - check credentials');
    }
  });

  // Cleanup created posts after each test
  afterEach(async () => {
    if (ghostAPI && createdPostIds.length > 0) {
      for (const postId of createdPostIds) {
        try {
          await ghostAPI.deletePost(postId);
        } catch (error) {
        }
      }
      createdPostIds = [];
    }

    // Clean up any local files created during the test
    const articlesDir = path.join(context.vaultPath, 'articles');
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          try {
            fs.unlinkSync(path.join(articlesDir, file));
          } catch (error) {
          }
        }
      }
    }
  });

  test("should sync all posts from Ghost using Sync All command", async () => {
    // Create 3 test posts in Ghost with different content
    const testPosts = [
      {
        title: "Test Post 1 - Sync All",
        slug: "test-post-1-sync-all",
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: "paragraph",
                children: [
                  {
                    type: "text",
                    text: "This is the first test post for sync all command.",
                    format: 0,
                    style: "",
                    mode: "normal",
                    detail: 0,
                    version: 1
                  }
                ],
                direction: "ltr",
                format: "",
                indent: 0,
                version: 1
              }
            ],
            direction: "ltr",
            format: "",
            indent: 0,
            type: "root",
            version: 1
          }
        }),
        status: "published",
        visibility: "public",
        tags: [{ name: "test" }, { name: "sync-all" }]
      },
      {
        title: "Test Post 2 - Sync All",
        slug: "test-post-2-sync-all",
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: "paragraph",
                children: [
                  {
                    type: "text",
                    text: "This is the second test post with ",
                    format: 0,
                    style: "",
                    mode: "normal",
                    detail: 0,
                    version: 1
                  },
                  {
                    type: "text",
                    text: "bold text",
                    format: 1,
                    style: "",
                    mode: "normal",
                    detail: 0,
                    version: 1
                  },
                  {
                    type: "text",
                    text: " and formatting.",
                    format: 0,
                    style: "",
                    mode: "normal",
                    detail: 0,
                    version: 1
                  }
                ],
                direction: "ltr",
                format: "",
                indent: 0,
                version: 1
              }
            ],
            direction: "ltr",
            format: "",
            indent: 0,
            type: "root",
            version: 1
          }
        }),
        status: "published",
        visibility: "public",
        tags: [{ name: "test" }, { name: "formatting" }]
      },
      {
        title: "Test Post 3 - Sync All",
        slug: "test-post-3-sync-all",
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: "heading",
                children: [
                  {
                    type: "text",
                    text: "Heading Example",
                    format: 0,
                    style: "",
                    mode: "normal",
                    detail: 0,
                    version: 1
                  }
                ],
                direction: "ltr",
                format: "",
                indent: 0,
                tag: "h2",
                version: 1
              },
              {
                type: "paragraph",
                children: [
                  {
                    type: "text",
                    text: "This is the third test post with a heading and multiple paragraphs.",
                    format: 0,
                    style: "",
                    mode: "normal",
                    detail: 0,
                    version: 1
                  }
                ],
                direction: "ltr",
                format: "",
                indent: 0,
                version: 1
              }
            ],
            direction: "ltr",
            format: "",
            indent: 0,
            type: "root",
            version: 1
          }
        }),
        status: "published",
        visibility: "public",
        tags: [{ name: "test" }, { name: "headings" }]
      }
    ];

    // Create the test posts in Ghost
    for (const postData of testPosts) {
      const createdPost = await ghostAPI.createPost(postData);
      createdPostIds.push(createdPost.id);
    }

    // Wait a moment for Ghost to process the posts
    await new Promise(resolve => setTimeout(resolve, 1000));

    await executeCommand(context, 'Ghost Sync: Sync all from Ghost');

    // Wait for the sync operation to complete with longer timeout for sync-all operations
    await expectNotice(context, "Synced", 15000);

    // Verify each post was created as a local file using expectPostFile helper
    await expectPostFile(context, "test-post-1-sync-all", {
      title: "Test Post 1 - Sync All",
      slug: "test-post-1-sync-all",
      status: "published",
      visibility: "public",
      tags: ["test", "sync-all"],
      content: "This is the first test post for sync all command."
    });

    await expectPostFile(context, "test-post-2-sync-all", {
      title: "Test Post 2 - Sync All",
      slug: "test-post-2-sync-all",
      status: "published",
      visibility: "public",
      tags: ["test", "formatting"],
      content: "**bold text**"
    });

    await expectPostFile(context, "test-post-3-sync-all", {
      title: "Test Post 3 - Sync All",
      slug: "test-post-3-sync-all",
      status: "published",
      visibility: "public",
      tags: ["test", "headings"],
      content: "## Heading Example"
    });
  });
});
