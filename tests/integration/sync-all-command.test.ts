import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ObsidianGhostAPI } from '../../src/api/ghost-api';
import { ContentConverter } from '../../src/utils/content-converter';

describe('Sync All Command - Integration Test', () => {
  let ghostAPI: ObsidianGhostAPI;
  let mockAPI: any;

  beforeEach(() => {
    // Create a mock API that simulates the Ghost Admin API
    mockAPI = {
      posts: {
        browse: vi.fn()
      }
    };

    // Create the ObsidianGhostAPI instance with a properly formatted test key
    const testKey = '123456789012345678901234:1234567890123456789012345678901234567890123456789012345678901234';
    ghostAPI = new ObsidianGhostAPI('http://test.com', testKey);

    // Replace the internal API with our mock
    (ghostAPI as any).api = mockAPI;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should simulate the complete Sync All workflow with multiple posts', async () => {
    // Create test posts that simulate real Ghost posts with different content types
    const testPosts = [
      {
        id: 'post-1',
        title: 'First Test Post',
        slug: 'first-test-post',
        status: 'published',
        visibility: 'public',
        created_at: '2024-01-01T10:00:00.000Z',
        updated_at: '2024-01-01T10:00:00.000Z',
        published_at: '2024-01-01T10:00:00.000Z',
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'This is the first test post with simple content.',
                    format: 0,
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  }
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                version: 1
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1
          }
        }),
        html: '<p>This is the first test post with simple content.</p>',
        tags: [
          { name: 'test', slug: 'test' },
          { name: 'sync-all', slug: 'sync-all' }
        ],
        authors: [
          { name: 'Test Author', slug: 'test-author' }
        ],
        primary_tag: { name: 'test', slug: 'test' },
        primary_author: { name: 'Test Author', slug: 'test-author' }
      },
      {
        id: 'post-2',
        title: 'Second Test Post with Formatting',
        slug: 'second-test-post-formatting',
        status: 'published',
        visibility: 'public',
        created_at: '2024-01-02T10:00:00.000Z',
        updated_at: '2024-01-02T10:00:00.000Z',
        published_at: '2024-01-02T10:00:00.000Z',
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'This post has ',
                    format: 0,
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  },
                  {
                    type: 'text',
                    text: 'bold text',
                    format: 1, // Bold format
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  },
                  {
                    type: 'text',
                    text: ' and ',
                    format: 0,
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  },
                  {
                    type: 'text',
                    text: 'italic text',
                    format: 2, // Italic format
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  },
                  {
                    type: 'text',
                    text: '.',
                    format: 0,
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  }
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                version: 1
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1
          }
        }),
        html: '<p>This post has <strong>bold text</strong> and <em>italic text</em>.</p>',
        tags: [
          { name: 'formatting', slug: 'formatting' },
          { name: 'test', slug: 'test' }
        ],
        authors: [
          { name: 'Test Author', slug: 'test-author' }
        ],
        primary_tag: { name: 'formatting', slug: 'formatting' },
        primary_author: { name: 'Test Author', slug: 'test-author' }
      },
      {
        id: 'post-3',
        title: 'Third Test Post with Heading',
        slug: 'third-test-post-heading',
        status: 'published',
        visibility: 'public',
        created_at: '2024-01-03T10:00:00.000Z',
        updated_at: '2024-01-03T10:00:00.000Z',
        published_at: '2024-01-03T10:00:00.000Z',
        lexical: JSON.stringify({
          root: {
            children: [
              {
                type: 'heading',
                children: [
                  {
                    type: 'text',
                    text: 'Important Heading',
                    format: 0,
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  }
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                tag: 'h2',
                version: 1
              },
              {
                type: 'paragraph',
                children: [
                  {
                    type: 'text',
                    text: 'This post demonstrates heading conversion and multiple paragraphs.',
                    format: 0,
                    style: '',
                    mode: 'normal',
                    detail: 0,
                    version: 1
                  }
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                version: 1
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1
          }
        }),
        html: '<h2>Important Heading</h2><p>This post demonstrates heading conversion and multiple paragraphs.</p>',
        tags: [
          { name: 'headings', slug: 'headings' },
          { name: 'structure', slug: 'structure' }
        ],
        authors: [
          { name: 'Test Author', slug: 'test-author' }
        ],
        primary_tag: { name: 'headings', slug: 'headings' },
        primary_author: { name: 'Test Author', slug: 'test-author' }
      }
    ];

    // Mock the API to return all posts in a single page (simulating successful pagination)
    mockAPI.posts.browse.mockResolvedValueOnce(Object.assign(testPosts, {
      meta: { pagination: { next: null } }
    }));

    console.log('🔄 Simulating Sync All command execution...');

    // Step 1: Fetch all posts (this is what the Sync All command does)
    const allPosts = await ghostAPI.getAllPosts();

    // Verify we got all posts with content
    expect(allPosts).toHaveLength(3);
    expect(allPosts[0].title).toBe('First Test Post');
    expect(allPosts[1].title).toBe('Second Test Post with Formatting');
    expect(allPosts[2].title).toBe('Third Test Post with Heading');

    // Verify all posts have lexical content
    for (const post of allPosts) {
      expect(post.lexical).toBeDefined();
      expect(post.lexical).not.toBe('');
      expect(post.lexical).not.toBeNull();
    }

    console.log('✅ Successfully fetched all posts with content');

    // Step 2: Convert each post to article format (this is what happens during sync)
    const convertedArticles = [];

    for (const post of allPosts) {
      console.log(`🔄 Converting post: ${post.title}`);

      try {
        const articleContent = await ContentConverter.convertGhostPostToArticle(post);
        convertedArticles.push({
          post,
          articleContent,
          fileName: `${post.slug}.md`
        });

        console.log(`✅ Successfully converted: ${post.title}`);
      } catch (error) {
        console.error(`❌ Failed to convert post: ${post.title}`, error);
        throw error;
      }
    }

    // Verify all posts were converted successfully
    expect(convertedArticles).toHaveLength(3);

    // Step 3: Verify the converted content is correct
    console.log('🔍 Verifying converted content...');

    // Check first post
    const firstArticle = convertedArticles[0];
    expect(firstArticle.articleContent).toContain('Title: "First Test Post"');
    expect(firstArticle.articleContent).toContain('Slug: "first-test-post"');
    expect(firstArticle.articleContent).toContain('Status: "published"');
    expect(firstArticle.articleContent).toContain('- test');
    expect(firstArticle.articleContent).toContain('- sync-all');
    expect(firstArticle.articleContent).toContain('This is the first test post with simple content.');

    // Check second post (with formatting)
    const secondArticle = convertedArticles[1];
    expect(secondArticle.articleContent).toContain('Title: "Second Test Post with Formatting"');
    expect(secondArticle.articleContent).toContain('Slug: "second-test-post-formatting"');
    expect(secondArticle.articleContent).toContain('**bold text**');
    expect(secondArticle.articleContent).toContain('*italic text*');

    // Check third post (with heading)
    const thirdArticle = convertedArticles[2];
    expect(thirdArticle.articleContent).toContain('Title: "Third Test Post with Heading"');
    expect(thirdArticle.articleContent).toContain('Slug: "third-test-post-heading"');
    expect(thirdArticle.articleContent).toContain('## Important Heading');
    expect(thirdArticle.articleContent).toContain('This post demonstrates heading conversion');

    console.log('🎉 Sync All simulation completed successfully!');
    console.log(`📊 Summary: Fetched and converted ${convertedArticles.length} posts`);

    // Verify the API was called with correct parameters
    expect(mockAPI.posts.browse).toHaveBeenCalledTimes(1);
    expect(mockAPI.posts.browse).toHaveBeenCalledWith({
      limit: 100,
      page: 1,
      formats: 'lexical',
      include: 'tags,authors'
    });
  });
});
