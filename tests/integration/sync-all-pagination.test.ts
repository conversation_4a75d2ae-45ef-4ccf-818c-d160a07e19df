import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ObsidianGhostAPI } from '../../src/api/ghost-api';

describe('Sync All - Pagination Integration', () => {
  let ghostAPI: ObsidianGhostAPI;
  let mockAPI: any;

  beforeEach(() => {
    // Create a mock API that simulates the Ghost Admin API
    mockAPI = {
      posts: {
        browse: vi.fn()
      }
    };

    // Create the ObsidianGhostAPI instance with a properly formatted test key
    const testKey = '123456789012345678901234:1234567890123456789012345678901234567890123456789012345678901234';
    ghostAPI = new ObsidianGhostAPI('http://test.com', testKey);

    // Replace the internal API with our mock
    (ghostAPI as any).api = mockAPI;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should fetch all posts with lexical content using pagination', async () => {
    // Mock 3 pages of posts with lexical content
    const page1Posts = Array.from({ length: 100 }, (_, i) => ({
      id: `post-${i + 1}`,
      title: `Post ${i + 1}`,
      slug: `post-${i + 1}`,
      lexical: JSON.stringify({
        root: {
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: `Content for post ${i + 1}`,
              format: 0,
              style: '',
              mode: 'normal',
              detail: 0,
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      }),
      status: 'published',
      visibility: 'public',
      tags: [{ name: 'test' }],
      authors: [{ name: 'Test Author' }]
    }));

    const page2Posts = Array.from({ length: 100 }, (_, i) => ({
      id: `post-${i + 101}`,
      title: `Post ${i + 101}`,
      slug: `post-${i + 101}`,
      lexical: JSON.stringify({
        root: {
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: `Content for post ${i + 101}`,
              format: 0,
              style: '',
              mode: 'normal',
              detail: 0,
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      }),
      status: 'published',
      visibility: 'public',
      tags: [{ name: 'test' }],
      authors: [{ name: 'Test Author' }]
    }));

    const page3Posts = Array.from({ length: 50 }, (_, i) => ({
      id: `post-${i + 201}`,
      title: `Post ${i + 201}`,
      slug: `post-${i + 201}`,
      lexical: JSON.stringify({
        root: {
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: `Content for post ${i + 201}`,
              format: 0,
              style: '',
              mode: 'normal',
              detail: 0,
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1
        }
      }),
      status: 'published',
      visibility: 'public',
      tags: [{ name: 'test' }],
      authors: [{ name: 'Test Author' }]
    }));

    // Mock the API responses for pagination
    mockAPI.posts.browse
      .mockResolvedValueOnce(Object.assign(page1Posts, {
        meta: { pagination: { next: 2 } }
      }))
      .mockResolvedValueOnce(Object.assign(page2Posts, {
        meta: { pagination: { next: 3 } }
      }))
      .mockResolvedValueOnce(Object.assign(page3Posts, {
        meta: { pagination: { next: null } }
      }));

    // Call getAllPosts (which is used by Sync All)
    const result = await ghostAPI.getAllPosts();

    // Verify all posts were fetched
    expect(result).toHaveLength(250);
    expect(result[0].title).toBe('Post 1');
    expect(result[99].title).toBe('Post 100');
    expect(result[100].title).toBe('Post 101');
    expect(result[199].title).toBe('Post 200');
    expect(result[200].title).toBe('Post 201');
    expect(result[249].title).toBe('Post 250');

    // Verify all posts have lexical content
    for (const post of result) {
      expect(post.lexical).toBeDefined();
      expect(post.lexical).toContain('Content for post');

      // Verify lexical content is valid JSON
      const lexicalData = JSON.parse(post.lexical);
      expect(lexicalData.root).toBeDefined();
      expect(lexicalData.root.type).toBe('root');
    }

    // Verify the API was called with correct parameters
    expect(mockAPI.posts.browse).toHaveBeenCalledTimes(3);

    // Check first call
    expect(mockAPI.posts.browse).toHaveBeenNthCalledWith(1, {
      limit: 100,
      page: 1,
      formats: 'lexical',
      include: 'tags,authors'
    });

    // Check second call
    expect(mockAPI.posts.browse).toHaveBeenNthCalledWith(2, {
      limit: 100,
      page: 2,
      formats: 'lexical',
      include: 'tags,authors'
    });

    // Check third call
    expect(mockAPI.posts.browse).toHaveBeenNthCalledWith(3, {
      limit: 100,
      page: 3,
      formats: 'lexical',
      include: 'tags,authors'
    });
  });

  it('should handle posts without lexical content gracefully', async () => {
    // Mock posts where some have lexical content and some don't
    const postsWithMixedContent = [
      {
        id: 'post-1',
        title: 'Post with Lexical',
        slug: 'post-with-lexical',
        lexical: JSON.stringify({
          root: {
            children: [{
              type: 'paragraph',
              children: [{
                type: 'text',
                text: 'This post has lexical content',
                format: 0,
                style: '',
                mode: 'normal',
                detail: 0,
                version: 1
              }],
              direction: 'ltr',
              format: '',
              indent: 0,
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1
          }
        }),
        html: '<p>This post has lexical content</p>',
        status: 'published'
      },
      {
        id: 'post-2',
        title: 'Post without Lexical',
        slug: 'post-without-lexical',
        lexical: null,
        html: '<p>This post only has HTML content</p>',
        status: 'published'
      },
      {
        id: 'post-3',
        title: 'Post with Empty Lexical',
        slug: 'post-with-empty-lexical',
        lexical: '',
        html: '<p>This post has empty lexical content</p>',
        status: 'published'
      }
    ];

    mockAPI.posts.browse.mockResolvedValueOnce(Object.assign(postsWithMixedContent, {
      meta: { pagination: { next: null } }
    }));

    const result = await ghostAPI.getAllPosts();

    expect(result).toHaveLength(3);

    // Verify the post with lexical content
    expect(result[0].lexical).toBeDefined();
    expect(result[0].lexical).toContain('This post has lexical content');

    // Verify posts without lexical content are still included
    expect(result[1].lexical).toBeNull();
    expect(result[1].html).toBe('<p>This post only has HTML content</p>');

    expect(result[2].lexical).toBe('');
    expect(result[2].html).toBe('<p>This post has empty lexical content</p>');
  });

  it('should include rate limiting delays between pagination requests', async () => {
    const startTime = Date.now();

    // Mock 3 pages to test rate limiting - need to return 100 posts per page to trigger pagination
    const page1Posts = Array.from({ length: 100 }, (_, i) => ({
      id: `post-${i + 1}`,
      title: `Post ${i + 1}`,
      slug: `post-${i + 1}`,
      lexical: JSON.stringify({ root: { children: [], type: 'root' } })
    }));

    const page2Posts = Array.from({ length: 100 }, (_, i) => ({
      id: `post-${i + 101}`,
      title: `Post ${i + 101}`,
      slug: `post-${i + 101}`,
      lexical: JSON.stringify({ root: { children: [], type: 'root' } })
    }));

    const page3Posts = Array.from({ length: 10 }, (_, i) => ({
      id: `post-${i + 201}`,
      title: `Post ${i + 201}`,
      slug: `post-${i + 201}`,
      lexical: JSON.stringify({ root: { children: [], type: 'root' } })
    }));

    mockAPI.posts.browse
      .mockResolvedValueOnce(Object.assign(page1Posts, {
        meta: { pagination: { next: 2 } }
      }))
      .mockResolvedValueOnce(Object.assign(page2Posts, {
        meta: { pagination: { next: 3 } }
      }))
      .mockResolvedValueOnce(Object.assign(page3Posts, {
        meta: { pagination: { next: null } }
      }));

    await ghostAPI.getAllPosts();

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Should have at least 200ms delay (100ms * 2 delays between 3 pages)
    // We allow some tolerance for test execution time
    expect(duration).toBeGreaterThan(150);

    // Verify all pages were fetched
    expect(mockAPI.posts.browse).toHaveBeenCalledTimes(3);
  });
});
